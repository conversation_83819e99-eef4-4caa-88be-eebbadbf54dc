using FleetXQ.Application.Common.Interfaces;
using FleetXQ.Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace FleetXQ.Infrastructure.Data;

/// <summary>
/// Application database context implementation
/// </summary>
public class ApplicationDbContext : DbContext, IApplicationDbContext
{
    /// <summary>
    /// Initializes a new instance of the <see cref="ApplicationDbContext"/> class
    /// </summary>
    /// <param name="options">The database context options</param>
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// Gets or sets the Users DbSet
    /// </summary>
    public DbSet<User> Users { get; set; } = null!;

    /// <summary>
    /// Gets or sets the Vehicles DbSet
    /// </summary>
    public DbSet<Vehicle> Vehicles { get; set; } = null!;

    /// <summary>
    /// Gets or sets the Drivers DbSet
    /// </summary>
    public DbSet<Driver> Drivers { get; set; } = null!;

    /// <summary>
    /// Gets or sets the Alerts DbSet
    /// </summary>
    public DbSet<Alert> Alerts { get; set; } = null!;

    /// <summary>
    /// Gets or sets the Trips DbSet
    /// </summary>
    public DbSet<Trip> Trips { get; set; } = null!;

    /// <summary>
    /// Configures the model that was discovered by convention from the entity types
    /// </summary>
    /// <param name="modelBuilder">The builder being used to construct the model for this context</param>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure User entity
        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Username).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Email).IsRequired().HasMaxLength(255);
            entity.Property(e => e.PasswordHash).IsRequired().HasMaxLength(255);
            entity.Property(e => e.FirstName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.LastName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Role).IsRequired();
            entity.Property(e => e.IsActive).IsRequired();
            entity.Property(e => e.IsEmailConfirmed).IsRequired();
            entity.Property(e => e.FailedLoginAttempts).IsRequired();
            
            entity.HasIndex(e => e.Username).IsUnique();
            entity.HasIndex(e => e.Email).IsUnique();
        });

        // Configure Vehicle entity
        modelBuilder.Entity<Vehicle>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.LicensePlate).IsRequired().HasMaxLength(20);
            entity.Property(e => e.VehicleType).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Status).IsRequired();
            entity.Property(e => e.Year).IsRequired();
            entity.Property(e => e.Make).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Model).IsRequired().HasMaxLength(50);
            entity.Property(e => e.VIN).HasMaxLength(17);
            
            entity.HasIndex(e => e.LicensePlate).IsUnique();
            entity.HasIndex(e => e.VIN).IsUnique();
        });

        // Configure Driver entity
        modelBuilder.Entity<Driver>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.FirstName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.LastName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.LicenseNumber).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Status).IsRequired();
            entity.Property(e => e.PhoneNumber).HasMaxLength(20);
            entity.Property(e => e.Email).HasMaxLength(255);
            
            entity.HasIndex(e => e.LicenseNumber).IsUnique();
        });

        // Configure Alert entity
        modelBuilder.Entity<Alert>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Type).IsRequired();
            entity.Property(e => e.Severity).IsRequired();
            entity.Property(e => e.Status).IsRequired();
            entity.Property(e => e.Message).IsRequired().HasMaxLength(1000);
            entity.Property(e => e.Description).HasMaxLength(2000);
            
            entity.HasOne<Vehicle>()
                .WithMany()
                .HasForeignKey(e => e.VehicleId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure Trip entity
        modelBuilder.Entity<Trip>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Status).IsRequired();
            entity.Property(e => e.StartLocation).HasMaxLength(500);
            entity.Property(e => e.EndLocation).HasMaxLength(500);
            
            entity.HasOne<Vehicle>()
                .WithMany()
                .HasForeignKey(e => e.VehicleId)
                .OnDelete(DeleteBehavior.Cascade);
                
            entity.HasOne<Driver>()
                .WithMany()
                .HasForeignKey(e => e.DriverId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure value objects
        ConfigureValueObjects(modelBuilder);
    }

    /// <summary>
    /// Configures value objects in the model
    /// </summary>
    /// <param name="modelBuilder">The model builder</param>
    private static void ConfigureValueObjects(ModelBuilder modelBuilder)
    {
        // Configure Location value object for Vehicle
        modelBuilder.Entity<Vehicle>().OwnsOne(v => v.CurrentLocation, location =>
        {
            location.Property(l => l.Latitude).HasColumnName("CurrentLatitude");
            location.Property(l => l.Longitude).HasColumnName("CurrentLongitude");
        });

        // Configure FuelLevel value object for Vehicle
        modelBuilder.Entity<Vehicle>().OwnsOne(v => v.FuelLevel, fuel =>
        {
            fuel.Property(f => f.CurrentLevel).HasColumnName("FuelCurrentLevel");
            fuel.Property(f => f.Capacity).HasColumnName("FuelCapacity");
        });

        // Configure Speed value object for Vehicle
        modelBuilder.Entity<Vehicle>().OwnsOne(v => v.CurrentSpeed, speed =>
        {
            speed.Property(s => s.Value).HasColumnName("CurrentSpeedValue");
            speed.Property(s => s.Unit).HasColumnName("CurrentSpeedUnit");
        });
    }

    /// <summary>
    /// Saves all changes made in this context to the database
    /// </summary>
    /// <param name="cancellationToken">A cancellation token to observe while waiting for the task to complete</param>
    /// <returns>A task that represents the asynchronous save operation</returns>
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Update timestamps for entities that inherit from BaseEntity
        var entries = ChangeTracker.Entries()
            .Where(e => e.Entity is Domain.Common.BaseEntity && 
                       (e.State == EntityState.Added || e.State == EntityState.Modified));

        foreach (var entry in entries)
        {
            var entity = (Domain.Common.BaseEntity)entry.Entity;
            
            if (entry.State == EntityState.Added)
            {
                entity.SetCreatedAt(DateTime.UtcNow);
            }
            
            entity.SetUpdatedAt(DateTime.UtcNow);
        }

        return await base.SaveChangesAsync(cancellationToken);
    }
}
