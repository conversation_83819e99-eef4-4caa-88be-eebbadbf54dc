using FleetXQ.Api.Models;
using FleetXQ.Api.Tests.Builders;
using FleetXQ.Application.Features.Vehicles.Commands.CreateVehicle;
using FleetXQ.Application.Features.Vehicles.Commands.UpdateVehicleStatus;
using FleetXQ.Application.Features.Vehicles.DTOs;
using FleetXQ.Application.Features.Vehicles.Queries.GetVehicleById;
using FleetXQ.Application.Features.Vehicles.Queries.GetVehicleList;
using FleetXQ.Domain.Enums;
using System.Net;

namespace FleetXQ.Api.Tests.Integration;

/// <summary>
/// Integration tests for vehicles endpoints
/// </summary>
public class VehiclesIntegrationTests : ApiIntegrationTestBase
{
    public VehiclesIntegrationTests(IntegrationTestWebApplicationFactory factory) : base(factory)
    {
    }

    [Fact]
    public async Task GetVehicles_WithValidAuthentication_ShouldReturnVehicleList()
    {
        // Arrange
        var user = TestDataBuilder.CreateUser();
        var vehicles = TestDataBuilder.CreateVehicles(5).ToList();

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(user);
            context.Vehicles.AddRange(vehicles);
        });

        var token = CreateTestJwtToken(user.Id, user.Username, user.Email, new[] { user.Role.ToString() });
        SetAuthorizationHeader(token);

        // Act
        var (response, content) = await PostAsync<GetVehicleListQuery, PaginatedApiResponse<VehicleListDto>>(
            "/api/vehicles/list",
            new GetVehicleListQuery { PageNumber = 1, PageSize = 10 }
        );

        // Assert
        response.IsSuccessStatusCode.Should().BeTrue();
        content.Should().NotBeNull();
        content!.Success.Should().BeTrue();
        content.Data.Should().NotBeNull();
        content.Data!.Items.Should().HaveCount(5);
        content.Data.TotalCount.Should().Be(5);
        content.Data.PageNumber.Should().Be(1);
        content.Data.PageSize.Should().Be(10);
    }

    [Fact]
    public async Task GetVehicleById_WithExistingVehicle_ShouldReturnVehicle()
    {
        // Arrange
        var user = TestDataBuilder.CreateUser();
        var vehicle = TestDataBuilder.CreateVehicle(name: "Test Vehicle", licensePlate: "TEST-123");

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(user);
            context.Vehicles.Add(vehicle);
        });

        var token = CreateTestJwtToken(user.Id, user.Username, user.Email, new[] { user.Role.ToString() });
        SetAuthorizationHeader(token);

        // Act
        var (response, content) = await PostAsync<GetVehicleByIdQuery, ApiResponse<GetVehicleByIdResult>>(
            "/api/vehicles/by-id",
            new GetVehicleByIdQuery { VehicleId = vehicle.Id }
        );

        // Assert
        AssertSuccessResponse(response, content);
        content!.Data.Should().NotBeNull();
        content.Data!.Vehicle.Should().NotBeNull();
        content.Data.Vehicle!.Id.Should().Be(vehicle.Id);
        content.Data.Vehicle.Name.Should().Be("Test Vehicle");
        content.Data.Vehicle.LicensePlate.Should().Be("TEST-123");
    }

    [Fact]
    public async Task GetVehicleById_WithNonExistentVehicle_ShouldReturnNotFound()
    {
        // Arrange
        var user = TestDataBuilder.CreateUser();
        var nonExistentId = Guid.NewGuid();

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(user);
        });

        var token = CreateTestJwtToken(user.Id, user.Username, user.Email, new[] { user.Role.ToString() });
        SetAuthorizationHeader(token);

        // Act
        var (response, content) = await PostAsync<GetVehicleByIdQuery, ApiResponse<GetVehicleByIdResult>>(
            "/api/vehicles/by-id",
            new GetVehicleByIdQuery { VehicleId = nonExistentId }
        );

        // Assert
        AssertErrorResponse(response, content, HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task CreateVehicle_WithValidData_ShouldCreateVehicle()
    {
        // Arrange
        var adminUser = TestDataBuilder.CreateAdminUser();

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(adminUser);
        });

        var token = CreateTestJwtToken(adminUser.Id, adminUser.Username, adminUser.Email, new[] { adminUser.Role.ToString() });
        SetAuthorizationHeader(token);

        var createCommand = new CreateVehicleCommand
        {
            Name = "New Test Vehicle",
            LicensePlate = "NEW-123",
            VehicleType = "Truck",
            Year = 2023,
            Make = "Ford",
            Model = "F-150",
            VIN = "1FTFW1ET5DFC12345"
        };

        // Act
        var (response, content) = await PostAsync<CreateVehicleCommand, ApiResponse<CreateVehicleResult>>(
            "/api/vehicles",
            createCommand
        );

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);
        content.Should().NotBeNull();
        content!.Success.Should().BeTrue();
        content.Data.Should().NotBeNull();
        content.Data!.Vehicle.Should().NotBeNull();
        content.Data.Vehicle!.Name.Should().Be("New Test Vehicle");
        content.Data.Vehicle.LicensePlate.Should().Be("NEW-123");

        // Verify vehicle was actually created in database
        using var dbContext = GetDbContext();
        var createdVehicle = await dbContext.Vehicles.FindAsync(content.Data.Vehicle.Id);
        createdVehicle.Should().NotBeNull();
        createdVehicle!.Name.Should().Be("New Test Vehicle");
    }

    [Fact]
    public async Task CreateVehicle_WithDuplicateLicensePlate_ShouldReturnBadRequest()
    {
        // Arrange
        var adminUser = TestDataBuilder.CreateAdminUser();
        var existingVehicle = TestDataBuilder.CreateVehicle(licensePlate: "DUPLICATE-123");

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(adminUser);
            context.Vehicles.Add(existingVehicle);
        });

        var token = CreateTestJwtToken(adminUser.Id, adminUser.Username, adminUser.Email, new[] { adminUser.Role.ToString() });
        SetAuthorizationHeader(token);

        var createCommand = new CreateVehicleCommand
        {
            Name = "Another Vehicle",
            LicensePlate = "DUPLICATE-123", // Same license plate
            VehicleType = "Car",
            Year = 2023,
            Make = "Toyota",
            Model = "Camry",
            VIN = "1FTFW1ET5DFC54321"
        };

        // Act
        var (response, content) = await PostAsync<CreateVehicleCommand, ApiResponse<CreateVehicleResult>>(
            "/api/vehicles",
            createCommand
        );

        // Assert
        AssertErrorResponse(response, content, HttpStatusCode.BadRequest);
        content!.Message.Should().Contain("license plate");
    }

    [Fact]
    public async Task CreateVehicle_WithInvalidData_ShouldReturnBadRequest()
    {
        // Arrange
        var adminUser = TestDataBuilder.CreateAdminUser();

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(adminUser);
        });

        var token = CreateTestJwtToken(adminUser.Id, adminUser.Username, adminUser.Email, new[] { adminUser.Role.ToString() });
        SetAuthorizationHeader(token);

        var createCommand = new CreateVehicleCommand
        {
            Name = "", // Invalid: empty name
            LicensePlate = "INVALID",
            VehicleType = "Car",
            Year = 2023,
            Make = "Toyota",
            Model = "Camry",
            VIN = "1FTFW1ET5DFC54321"
        };

        // Act
        var (response, content) = await PostAsync<CreateVehicleCommand, ApiResponse<CreateVehicleResult>>(
            "/api/vehicles",
            createCommand
        );

        // Assert
        AssertErrorResponse(response, content, HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task CreateVehicle_WithUserRole_ShouldReturnForbidden()
    {
        // Arrange
        var regularUser = TestDataBuilder.CreateUser(role: UserRole.User);

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(regularUser);
        });

        var token = CreateTestJwtToken(regularUser.Id, regularUser.Username, regularUser.Email, new[] { regularUser.Role.ToString() });
        SetAuthorizationHeader(token);

        var createCommand = new CreateVehicleCommand
        {
            Name = "Test Vehicle",
            LicensePlate = "TEST-123",
            VehicleType = "Car",
            Year = 2023,
            Make = "Toyota",
            Model = "Camry",
            VIN = "1FTFW1ET5DFC54321"
        };

        // Act
        var (response, content) = await PostAsync<CreateVehicleCommand, ApiResponse<CreateVehicleResult>>(
            "/api/vehicles",
            createCommand
        );

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Forbidden);
    }

    [Fact]
    public async Task UpdateVehicleStatus_WithValidData_ShouldUpdateStatus()
    {
        // Arrange
        var managerUser = TestDataBuilder.CreateManagerUser();
        var vehicle = TestDataBuilder.CreateVehicle(status: VehicleStatus.Available);

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(managerUser);
            context.Vehicles.Add(vehicle);
        });

        var token = CreateTestJwtToken(managerUser.Id, managerUser.Username, managerUser.Email, new[] { managerUser.Role.ToString() });
        SetAuthorizationHeader(token);

        var updateCommand = new UpdateVehicleStatusCommand
        {
            VehicleId = vehicle.Id,
            Status = VehicleStatus.InUse
        };

        // Act
        var (response, content) = await PutAsync<UpdateVehicleStatusCommand, ApiResponse<UpdateVehicleStatusResult>>(
            "/api/vehicles/status",
            updateCommand
        );

        // Assert
        AssertSuccessResponse(response, content);
        content!.Data.Should().NotBeNull();
        content.Data!.Vehicle.Should().NotBeNull();
        content.Data.Vehicle!.Status.Should().Be(VehicleStatus.InUse);

        // Verify status was actually updated in database
        using var dbContext = GetDbContext();
        var updatedVehicle = await dbContext.Vehicles.FindAsync(vehicle.Id);
        updatedVehicle.Should().NotBeNull();
        updatedVehicle!.Status.Should().Be(VehicleStatus.InUse);
    }

    [Fact]
    public async Task UpdateVehicleStatus_WithNonExistentVehicle_ShouldReturnNotFound()
    {
        // Arrange
        var managerUser = TestDataBuilder.CreateManagerUser();
        var nonExistentId = Guid.NewGuid();

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(managerUser);
        });

        var token = CreateTestJwtToken(managerUser.Id, managerUser.Username, managerUser.Email, new[] { managerUser.Role.ToString() });
        SetAuthorizationHeader(token);

        var updateCommand = new UpdateVehicleStatusCommand
        {
            VehicleId = nonExistentId,
            Status = VehicleStatus.InUse
        };

        // Act
        var (response, content) = await PutAsync<UpdateVehicleStatusCommand, ApiResponse<UpdateVehicleStatusResult>>(
            "/api/vehicles/status",
            updateCommand
        );

        // Assert
        AssertErrorResponse(response, content, HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task GetVehicles_WithoutAuthentication_ShouldReturnUnauthorized()
    {
        // Arrange
        ClearAuthorizationHeader();

        // Act
        var response = await HttpClient.GetAsync("/api/vehicles");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task GetVehicles_WithPagination_ShouldReturnCorrectPage()
    {
        // Arrange
        var user = TestDataBuilder.CreateUser();
        var vehicles = TestDataBuilder.CreateVehicles(15).ToList();

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(user);
            context.Vehicles.AddRange(vehicles);
        });

        var token = CreateTestJwtToken(user.Id, user.Username, user.Email, new[] { user.Role.ToString() });
        SetAuthorizationHeader(token);

        // Act
        var (response, content) = await PostAsync<GetVehicleListQuery, PaginatedApiResponse<VehicleListDto>>(
            "/api/vehicles/list",
            new GetVehicleListQuery { PageNumber = 2, PageSize = 5 }
        );

        // Assert
        response.IsSuccessStatusCode.Should().BeTrue();
        content.Should().NotBeNull();
        content!.Success.Should().BeTrue();
        content.Data.Should().NotBeNull();
        content.Data!.Items.Should().HaveCount(5);
        content.Data.TotalCount.Should().Be(15);
        content.Data.PageNumber.Should().Be(2);
        content.Data.PageSize.Should().Be(5);
        content.Data.TotalPages.Should().Be(3);
    }
}
